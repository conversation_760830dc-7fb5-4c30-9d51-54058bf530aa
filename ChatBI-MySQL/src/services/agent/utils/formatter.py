"""
Event formatting utilities for handling agent output events.
"""
import json
from src.utils.logger import logger
from agents import ItemHelpers


def format_event_message(event) -> dict:
    """
    Format events into readable message strings and return as JSON objects.

    Args:
        event: The event object to format

    Returns:
        dict: A formatted message dictionary
    """
    try:
        if (
                event.type == "raw_response_event"
                and hasattr(event.data, "delta")
                and event.data.delta
        ):
            return _handle_raw_response_event(event)
        elif event.type == "agent_thinking_event":
            return _handle_agent_thinking_event(event)
        elif event.type == "run_item_stream_event":
            return _handle_run_item_stream_event(event)
        else:
            # logger.warning(f"Unknown event: {event}")
            pass
    except Exception as e:
        logger.exception(f"Error formatting event: {e}, event: {event}")
        return {"type": "log", "content": str(e), "error": True}


def _handle_raw_response_event(event) -> dict:
    """
    Handle raw response events, typically containing JSON data.

    Args:
        event: The raw response event

    Returns:
        dict: A formatted message dictionary
    """
    try:
        parsed = json.loads(event.data.delta)
        if isinstance(parsed, dict):
            description = parsed.get("description")
            sql = parsed.get("sql")
            if description or sql:
                content = ""
                if description:
                    content += f"- 说明: {description}\n"
                if sql:
                    content += f"- SQL: {sql}\n"
                return {"type": "data", "content": f"\n\n{content}\n___\n"}
            else:
                return {
                    "type": "log",
                    "content": "".join(
                        [f"- {key}: {value}\n" for key, value in parsed.items()]) + "\n___\n",
                }
        else:
            return {"type": "data", "content": str(parsed)}
    except (json.JSONDecodeError, TypeError):
        return {"type": "data", "content": event.data.delta}


def _handle_agent_thinking_event(event) -> dict:
    """
    Handle agent thinking events.

    Args:
        event: The agent thinking event

    Returns:
        dict: A formatted message dictionary
    """
    return {"type": "thinking", "content": event.data.thinking}


def _handle_run_item_stream_event(event) -> dict:
    """
    Handle run item stream events.

    Args:
        event: The run item stream event

    Returns:
        dict: A formatted message dictionary
    """
    if event.item.type == "tool_call_item":
        return {
            "type": "tool_call_log",
            "content": f"{event.item.raw_item if hasattr(event.item, 'raw_item') else event.item}",
        }
    elif event.item.type == "tool_call_output_item":
        return _handle_tool_call_output_item(event)
    elif event.item.type == "handoff_output_item":
        # Handle handoff output items specifically
        # HandoffOutputItem doesn't have an output attribute, but we can access
        # the source and target agents
        source_agent = getattr(event.item, 'source_agent', None)
        target_agent = getattr(event.item, 'target_agent', None)

        if source_agent and target_agent:
            content = f"Handoff from {source_agent.name} to {target_agent.name}"
        else:
            content = "Handoff completed"

        return {
            "type": "handoff_log",
            "content": content
        }
    elif event.item.type == "message_output_item":
        return {
            "type": "log",
            "content": ItemHelpers.text_message_output(event.item),
        }


def _handle_tool_call_output_item(event) -> dict:
    """
    Handle tool call output items.

    Args:
        event: The tool call output event

    Returns:
        dict: A formatted message dictionary
    """
    output = str(event.item.output)
    # Check if the output is likely DDL content (adjust as needed)
    is_ddl_read_operator = "CREATE TABLE" in output or "TABLE_SCHEMA" in output
    return {
        "type": "tool_output",
        "content": "AI读取了DDL文件" if is_ddl_read_operator else output,
    }
